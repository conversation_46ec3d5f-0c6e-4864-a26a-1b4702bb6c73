"use strict";

/**
 * initialize DataTables method
 * @param datatables
 * @returns {boolean}
 */
function initializeDataTables(datatables) {
    $(".date_picker").flatpickr();
    $(".date_time_picker").flatpickr({
        enableTime: true,
        dateFormat: "Y-m-d H:i",
    });

    let keys = Object.keys(datatables);

    if (keys.length < 0) {
        return false;
    }
//kt_table_users //kt_table_names
    keys.forEach(function (tableKey, index) {
        let table = $("#kt_table_" + tableKey + "s");

        if (!table.length) {
            return;
        }

        let tableObj = new Object();
        let apiUrl = "/datatables/" + tableKey + "-list" +
            (datatables[tableKey]["key"]
                ? "/" + datatables[tableKey]["key"]
                : "");

        let $ajaxData = {
            url: apiUrl,
            data: {}
        };

        if (datatables[tableKey]["data"]) {
            $ajaxData.data = datatables[tableKey]["data"];
        }

        resolveData(table[0], $ajaxData.data)

        let tableOptions = {
            processing: true,
            serverSide: true,
            bAutoWidth: false,
            order: [[1, "desc"]],
            ajax: $ajaxData,
            columnDefs: datatables[tableKey]["columnDefs"]
                ? datatables[tableKey]["columnDefs"]
                : [],
            columns: datatables[tableKey]["columns"]
                ? datatables[tableKey]["columns"]
                : datatables[tableKey],
        };

        if (datatables[tableKey]["dom"]) {
            tableOptions.dom = datatables[tableKey]["dom"];
        }

        // Initialize DataTable for the current table
        window.dataTable = tableObj = table.DataTable(tableOptions);

        if (datatables[tableKey]["events"]) {
            datatables[tableKey]["events"](tableObj);
        }
    });
}

/**
 * Rescue attribute and value from selector
 *
 * @param {object} table
 * @param {object} data
 */
function resolveData(table, data) {
    let attr, node = "";

    Object.keys(table.attributes).forEach(e => {
        node = table.attributes[e];
        attr = node.nodeName.match(/^(data\-)/);

        if (!attr) {
            return;
        }

        data[node.nodeName.replace('data-', '')] = node.nodeValue;
    });

    return data;
}

$(document).ready(function () {
    let datatables = {
        // Here is single datatable example format. you can follow this format to initiate any table
        user: {
            data: function (data) {
                data.role_id = $("#filterByUserType").val();
                data.user_id = $("#filterByUser").val();
            },
            events: function (context) {
                $("#filterByUserType").on("change", function () {
                    context.draw();
                });
                $("#filterByUser").on("change", function () {
                    context.draw();
                });
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
            },
            columnDefs: [
                {targets: 3, orderable: false},
            ],
            columns: [
                {data: "id", title: "Sl"},
                {data: "name", title: "Name"},
                {data: "username", title: "Login ID"},
                {data: "email", title: "Email"},
                {data: "phone", title: "Contact #"},
                {data: "current_balance", title: "Credits"},
                {data: "balance_expired", title: "Expiry Date"},
                {data: "user_type", title: "User Type"},
                {data: "action", title: "Action"},
            ]
        },
        sender: {
            data: function (data) {
            },
            events: function (context) {
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
            },
            columnDefs: [
                {targets: 4, orderable: false},
            ],
            columns: [
                {data: "id", title: "Sl"},
                {data: "name", title: "Sender ID"},
                {
                    data: "company.name",
                    title: "Company",
                    render: function (data, type, row) {
                        return data ? data : "Unused"; // Fallback to "N/A" if company.name is null
                    }
                },
                {data: "is_default", title: "Default"},
                {data: "created_at", title: "Requested On"},
                {data: "executed_at", title: "Executed On"},
                {data: "status", title: "Status"},
                {data: "action", title: "Action"},
            ]
        },
        contact: {
            data: function (data) {
                data.operator = $("#filterByNameOperator").val();
                data.group = $("#filterByGroup").val();
                data.status = $("#filterByStatus").val();
            },
            events: function (context) {
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
                $("#filterByNameOperator").on("keyup change", function () {
                    context.draw();
                });
                $("#filterByGroup").on("change", function () {
                    context.draw();
                });
                $("#filterByStatus").on("change", function () {
                    context.draw();
                });
            },
            columnDefs: [
                {targets: 7, orderable: false},
            ],
            columns: [
                {data: "id", title: "Sl"},
                {data: "name", title: "Name"},
                {data: "phone", title: "Contact"},
                {data: "email", title: "Email"},
                {data: "group_name", title: "Group"},
                {data: "operator", title: "Operator"},
                {data: "status", title: "Status"},
                {data: "action", title: "Action"},
            ]
        },
        coverage: {
            data: function (data) {
            },
            events: function (context) {
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
            },
            columnDefs: [
                {targets: 6, orderable: false},
            ],
            columns: [
                {data: "id", title: "Sl"},
                {data: "operator", title: "Operator"},
                {data: "prefix", title: "Prefix"},
                {data: "masking_price", title: "Masking Price"},
                {data: "non_masking_price", title: "Non Masking Price"},
                {data: "status", title: "Status"},
                {data: "action", title: "Action"},
            ]
        },
        server: {
            data: function (data) {
            },
            events: function (context) {
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
            },
            columnDefs: [
                {targets: 6, orderable: false},
            ],
            columns: [
                {data: "id", title: "Sl"},
                {data: "name", title: "Name"},
                {data: "api_link", title: "Api"},
                {data: "api_key", title: "api key"},
                {data: "username", title: "Username"},
                {data: "status", title: "Status"},
                {data: "action", title: "Action"},
            ]
        },
        gateway: {
            data: function (data) {
            },
            events: function (context) {
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
            },
            columnDefs: [
                {targets: 6, orderable: false},
            ],
            columns: [
                {data: "id", title: "Sl"},
                {data: "name", title: "Name"},
                {data: "msisdn", title: "MSISDN"},
                {data: "username", title: "Username"},
                {data: "password", title: "Password"},
                {data: "test_mode", title: "Test mode"},
                {data: "status", title: "Status"},
                {data: "action", title: "Action"},
            ]
        },
        recharge: {
            data: function (data) {
            },
            events: function (context) {
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
            },
            columnDefs: [
                {targets: 4, orderable: false},
            ],
            columns: [
                {data: "id", title: "Sl"},
                {data: "recharge_date", title: "Date"},
                {data: "payment.gateway", title: "Gateway"},
                {data: "payment.remarks", title: "Remarks"},
                {data: "payment.transaction_id", title: "Trans ID"},
                {data: "payment.amount", title: "Amount"},
                {data: "payment.payment_status", title: "Status"}
            ]
        },
        'account-recharge': [
            {data: "id", title: "Sl"},
            {data: "company.name", title: "Company"},
            {data: "recharge_date", title: "Date"},
            {data: "gateway", title: "Gateway"},
            {data: "remarks", title: "Remarks"},
            {data: "transaction_id", title: "Trans ID"},
            {data: "recharge_amount", title: "Amount"},
            {data: "payment_status", title: "Status"},
            {data: "action", title: "Action"},
        ],
        template: {
            data: function (data) {
            },
            events: function (context) {
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
            },
            columns: [
                {data: "id", title: "Sl"},
                {data: "name", title: "Name"},
                {data: "text", title: "Text"},
                {data: "created_at", title: "Created On"},
                {data: "action", title: "Action"},
            ]
        },
        content: {
            data: function (data) {
            },
            events: function (context) {
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
            },
            columns: [
                {data: "id", title: "Sl"},
                {data: "name", title: "Name"},
                {data: "text", title: "Text"},
                {data: "action", title: "Action"},
            ]
        },
        'today-detail': {
            data: function (data) {},
            events: function (context) {
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
            },
            columns: [
                { data: "mobile", name: 'messages.phone_number', title: 'Mobile' },
                { data: "sent_time", name: 'messages.schedule_at', title: 'Sent Time' },
                { data: "sender_name", name: 'senders.name', title: 'Sender Name' },
                { data: "operator", name: 'senders.name', title: 'Operator' },
                { data: "charge_per_sms", name: 'senders.name', title: 'Charge Per Sms' },
                { data: "sms_text", name: 'messages.sms_content', title: 'Sms Text' },
                { data: "api_response", name: 'messages.api_response', title: 'API Response' },
                { data: "status", name: 'messages.status', title: 'Status' },
            ]
        },
        transaction: {
            data: function (data) {},
            events: function (context) {
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
            },
            columns: [
                { data: "company.name", name: 'company_id', title: 'Company' },
                { data: "date", name: 'date', title: 'Date' },
                { data: "amount_in", name: 'amount_in', title: 'Amount In' },
                { data: "amount_out", name: 'amount_out', title: 'Amount Out' },
                { data: "remarks", name: 'remarks', title: 'Remarks' }
            ]
        }
    };

    initializeDataTables(datatables);
});


