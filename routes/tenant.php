<?php

use App\Http\Controllers\AccountController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\CoverageController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\OtherController;
use App\Http\Controllers\ReportsController;
use App\Http\Controllers\SenderController;
use App\Http\Controllers\TemplateController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('messages/dynamic-sms', [MessageController::class, 'getDynamicSms'])->name('messages.dynamic-sms.get');
    Route::post('messages/dynamic-sms', [MessageController::class, 'postDynamicSms'])->name('messages.dynamic-sms.post');
    Route::post('messages/group-sms', [MessageController::class, 'messagesGroupStore'])->name('messages.group.store');

    Route::get('/', [DashboardController::class, 'index'])->name('home');
    Route::resource('users', UserController::class);
    Route::resource('messages', MessageController::class);
    Route::resource('contacts', ContactController::class);
    Route::resource('templates', TemplateController::class);
    Route::resource('coverages', CoverageController::class);
    Route::resource('senders', SenderController::class);

    Route::get('/price-coverage', [OtherController::class, 'priceCoverage'])->name('price-coverage');
    Route::get('/developers', [OtherController::class, 'developers'])->name('developers');
    Route::post('/generate_api_key', [OtherController::class, 'generateApiKey'])->name('generate-api-key');
    Route::post('/add-group', [ContactController::class, 'addGroup'])->name('group');

    Route::prefix('account')->name('account.')->group(function () {
        Route::get('/overview', [AccountController::class, 'overview'])->name('overview');
        Route::get('/api-keys', [AccountController::class, 'apiKeys'])->name('api-keys');
        Route::get('/add-funds', [AccountController::class, 'addFunds'])->name('add-funds');
        Route::get('/recharges', [AccountController::class, 'rechargeHistory'])->name('recharge-history');
        Route::get('/logs', [AccountController::class, 'logs'])->name('logs');
        Route::get('/settings', [AccountController::class, 'settings'])->name('settings');
        Route::patch('/settings-update', [AccountController::class, 'settingsUpdate'])->name('settings.update');

        // Domain management routes - restricted to vendor roles and above
        Route::middleware(['role:super-admin|master-reseller|reseller'])->group(function () {
            Route::get('/domains', [AccountController::class, 'domains'])->name('domains');
            Route::post('/domains', [AccountController::class, 'storeDomain'])->name('domains.store');
            Route::post('/domains/{domainId}/verify', [AccountController::class, 'verifyDomain'])->name('domains.verify');
            Route::delete('/domains/{domainId}', [AccountController::class, 'destroyDomain'])->name('domains.destroy');
        });
    });
    Route::get('/recharges', [AccountController::class, 'recharge'])->name('recharges');

    // Reports Routes
    Route::prefix('reports')->middleware(['auth'])->name('reports.')->group(function () {
        Route::get('/recharges', [ReportsController::class, 'report'])
            ->name('recharges');
        Route::get('/transactions', [TransactionController::class, 'index'])
            ->name('transactions');
        Route::get('/view-dlr/{type}', [ReportsController::class, 'viewDlr'])
            ->name('view-dlr');



        // today's-dlr, today's-details, archived-dlr
        /* Route::get('/campaign-dlr/{type}', [ReportsController::class, 'campaignDlr'])
             ->name('campaign-dlr'); // todays-dlr, archived-dlr
         Route::get('/statistics/summary-logs', [ReportsController::class, 'summaryLogs'])
             ->name('statistics-summary-logs');
         Route::get('/statistics/details-logs', [ReportsController::class, 'detailsLogs'])
             ->name('statistics-details-logs');
         Route::get('/statistics/day-wise-logs', [ReportsController::class, 'dayWiseLogs'])
             ->name('statistics-day-wise-logs');
         Route::get('/statistics/api-sms-purpose', [ReportsController::class, 'apiSmsPurpose'])
             ->name('statistics-api-sms-purpose');
         Route::get('/scheduled-sms', [ReportsController::class, 'scheduledSms'])
             ->name('scheduled-sms');
         Route::get('/transactions', [ReportsController::class, 'transactions'])
             ->name('transactions');
         Route::get('/usages/today', [ReportsController::class, 'todayUsages'])
             ->name('usages-today');
         Route::get('/usages/last-7-days', [ReportsController::class, 'last7DaysUsages'])
             ->name('usages-last-7-days');
         Route::get('/usages/this-month', [ReportsController::class, 'thisMonthUsages'])
             ->name('usages-this-month');*/
    });

    Route::get('/{company}', [DashboardController::class, 'switch']);
});
