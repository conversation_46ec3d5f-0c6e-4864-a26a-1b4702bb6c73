<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Server extends Model
{
    use HasFactory;

    protected $fillable = [
        'api_link',
        'name',
        'api_key',
        'username',
        'password',
        'sending_limit',
        'time_base',
        'time_unit',
        'enable_service',
        'user_id',
        'status',
    ];

    /**
     * Get the default sender for the server.
     */
    public function defaultSender(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(Sender::class)
            ->where('is_server_default', true);
    }

    /**
     * Get all senders associated with the server.
     */
    public function senders()
    {
        return $this->hasMany(Sender::class);
    }
}
