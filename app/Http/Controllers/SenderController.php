<?php

namespace App\Http\Controllers;

use App\Helpers\Common;
use App\Http\Requests\StoreSenderRequest;
use App\Http\Requests\UpdateSenderRequest;
use App\Http\Services\SenderService;
use App\Models\Sender;
use App\Models\Server;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class SenderController extends Controller
{
    private $sender;

    public function __construct(SenderService $sender)
    {
        $this->sender = $sender;
        $this->middleware(['permission:sender-list|sender-create|sender-edit|sender-delete'], ['only' => ['index', 'show']]);
        $this->middleware(['permission:sender-create'], ['only' => ['create', 'store']]);
        $this->middleware(['permission:sender-edit'], ['only' => ['edit', 'update']]);
        $this->middleware(['permission:sender-delete'], ['only' => ['destroy']]);
    }

    /**
     * @throws \Yajra\DataTables\Exceptions\Exception
     * @throws \Exception
     */
    public function browse()
    {
        return datatables()
            ->of(Sender::with('company:id,name'))
            ->addColumn('created_at', function (Sender $name) {
                return $name->created_at->format('F j, Y, g:i a');
            })
            ->editColumn('executed_at', function (Sender $name) {
                return ! empty($name->executed_at) ? Carbon::parse($name->executed_at)->format('F j, Y, g:i a') : '-';
            })
            ->editColumn('is_default', function (Sender $name) {
                return $name->is_default ? 'Yes' : 'No';
            })
            ->editColumn('status', function (Sender $name) {
                return ucfirst($name->status);
            })

            ->addColumn('action', function (Sender $sender) {
                $action = '';
                $user = auth()->user();

                if ($user->hasRole('super-admin') || $sender->user_id == $user->id) {
                    $action .= '<button href="#" class="btn btn-danger btn-sm delete-button" data-endpoint="'.route('senders.destroy', $sender->id).'" >Delete</button>';
                }

                if (Auth::user()->hasPermissionTo('sender-edit')) {
                    $action .= ' <a href="'.route('senders.edit', $sender->id).'" class="btn btn-secondary btn-sm" >Edit</a>';
                }
                $action .= ' <button  href="#" data-endpoint="'.route('webapi.default', $sender->id).'" class="btn btn-success btn-sm make-default" >Mark as Default</a>';

                return $action;
            })
            ->rawColumns(['action'])
            ->toJson();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $names = Sender::all();

        // Fetch servers for the dropdown, accessible only to super-admin
        $servers = [];
        if (auth()->user()->hasRole('super-admin')) {
            $servers = Server::select('id', 'name')->get();
        }

        return view('senders.index', compact('names', 'servers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $senderStatus = Common::senderStatus();
        $senderIdFee = Common::senderIdFee();

        return view('senders.create', compact('senderStatus', 'senderIdFee'));
    }

    /**
     * Store a newly created sender.
     */
    public function store(StoreSenderRequest $request)
    {
        // Check if the sender name already exists
        $sender = Sender::where('name', $request->name)->first();
        if (! empty($sender)) {
            return response([
                'success' => false,
                'message' => 'This sender name already exists. Please contact the admin.',
            ], 404);
        }

        // Handle `is_server_default` based on user role
        $isServerDefault = Auth::user()->hasRole('super-admin') ? ($request->is_server_default ?? 0) : 0;

        // Initialize an array to store file paths
        $documentPaths = [];

        // Check if there are files in `required_documents` and upload each
        if ($request->hasFile('required_documents')) {
            foreach ($request->file('required_documents') as $file) {
                // Save to `documents` directory on the `public` disk
                $path = $file->store('documents', 'public');
                $documentPaths[] = $path; // Collect the stored file path
            }
        }

        // Create the sender with collected data
        $resp = Sender::create([
            'user_id' => Auth::user()->id,
            'server_id' => $request->server_id ?? null,
            'name' => $request->name,
            'is_default' => $request->is_default ?? 0,
            'is_server_default' => $isServerDefault,
            'is_masking' => is_numeric($request->name) ? 0 : 1,
            'status' => 'Pending Approved',
            'required_documents' => $documentPaths, // Save document paths as JSON array
        ]);

        return response([
            'success' => true,
            'message' => 'Sender has been created successfully.',
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Sender $sender)
    {
        $senderStatus = Common::senderStatus();
        $senderIdFee = Common::senderIdFee();

        // Fetch servers for the dropdown, accessible only to super-admin
        $servers = [];
        if (auth()->user()->hasRole('super-admin')) {
            $servers = Server::select('id', 'name')->get();
        }

        // Check if required_documents is cast to an array, otherwise decode it manually
        $requiredDocuments = collect($sender->required_documents)->toArray();

        return view('senders.edit', compact('sender', 'senderStatus', 'senderIdFee', 'servers', 'requiredDocuments'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @throws \App\Exceptions\ErrorMessageException
     */
    public function update(UpdateSenderRequest $request, Sender $sender)
    {

        // Check if the status is 'Approved' and server_id is required
        if ($request->status === 'Approved' && empty($request->server_id)) {
            return redirect()->back()->withErrors([
                'server_id' => 'You must select a server before approving this sender.',
            ]);
        }
        // Only super-admin can update `is_server_default`, otherwise it defaults to 0
        $isServerDefault = Auth::user()->hasRole('super-admin') ? ($request->is_server_default ?? 0) : 0;

        // Delete old documents if new ones are uploaded
        if ($request->hasFile('required_documents')) {
            foreach ($sender->required_documents ?? [] as $oldDocument) {
                if (Storage::disk('public')->exists($oldDocument)) {
                    Storage::disk('public')->delete($oldDocument);
                }
            }

            // Upload new documents
            $documentPaths = [];
            foreach ($request->file('required_documents') as $file) {
                $path = $file->store('documents', 'public'); // Store in 'public/documents' folder
                $documentPaths[] = $path;
            }

            $sender->required_documents = $documentPaths; // Update document paths in sender record
        }
        // Update other fields in sender record
        $sender->update([
            'name' => $request->name,
            'server_id' => $request->server_id,
            'is_default' => $request->is_default,
            'is_server_default' => $isServerDefault,
            'status' => $request->status,
            'executed_at' => $request->status == 'Approved' ? now() : null,
        ]);

        return redirect()->route('senders.index')
            ->with('success', 'Sender has been updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Sender $sender)
    {
        $user = auth()->user();

        if ($user->hasRole('super-admin') || $sender->user_id == $user->id) {
            $sender->delete();

            return response([
                'success' => true,
                'message' => 'Sender has been deleted successfully.',
            ]);
        }

        return response([
            'success' => false,
            'message' => 'You don\'t have permission to access.',
        ], 401);
    }

    /**
     * Mark the specified sender as default.
     */
    public function makeDefault(Sender $sender)
    {
        $this->sender->makeDefault($sender);

        return response([
            'success' => true,
            'message' => 'Sender marked as default successfully.',
        ]);
    }
}
