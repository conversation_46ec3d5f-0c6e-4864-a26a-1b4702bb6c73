<?php

namespace App\Http\Controllers;

use App\Helpers\Common;
use App\Http\Requests\StoreServerRequest;
use App\Http\Requests\UpdateServerRequest;
use App\Http\Services\ServerService;
use App\Models\Server;
use Illuminate\Http\Request;

class ServerController extends Controller
{
    private $server;

    public function __construct(ServerService $server)
    {
        $this->server = $server;
        $this->middleware(['permission:server-list|server-create|server-edit|server-delete'], ['only' => ['index', 'show']]);
        $this->middleware(['permission:server-create'], ['only' => ['create', 'store']]);
        $this->middleware(['permission:server-edit'], ['only' => ['edit', 'update']]);
        $this->middleware(['permission:server-delete'], ['only' => ['destroy']]);
    }

    public function browse(Request $request)
    {
        return datatables()->of(
            Server::query()
        )
            ->addColumn('action', function (Server $server) {
                $action = ' <a href="'.route('admin.servers.edit', $server->id).'" class="btn btn-secondary btn-sm">Edit</a>';
                $action .= ' <a href="'.route('admin.servers.show', $server->id).'" class="btn btn-success btn-sm">View</a>';

                return $action;
            })
            ->rawColumns(['action'])
            ->toJson();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('admin.servers.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $smsProviders = Common::smsProviders();

        return view('admin.servers.create', [
            'smsProviders' => $smsProviders,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreServerRequest $request)
    {
        $this->server->store($request);

        return redirect()->route('admin.servers.index')
            ->with('success', 'Server created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Server $server)
    {
        return view('admin.servers.show', compact('server'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Server $server)
    {
        $smsProviders = Common::smsProviders();

        return view('admin.servers.edit', [
            'server' => $server,
            'smsProviders' => $smsProviders,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateServerRequest $request, Server $server)
    {
        $this->server->update($request, $server);

        return redirect()->route('admin.servers.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Server $server)
    {
        $this->server->destroy($server);

        return redirect()->route('admin.servers.index');
    }
}
