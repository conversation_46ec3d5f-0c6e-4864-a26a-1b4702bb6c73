<?php

namespace App\Http\Services\SmsServers;

use App\Models\Server;
use Illuminate\Support\Str;

class SmsSendingBase
{
    /**
     * Server namespace
     *
     * @var string
     */
    private static $namespace = '\\App\\Http\\Services\\SmsServers\\Servers';

    /**
     * Server information
     *
     * @var null|\App\Models\Server
     */
    protected static $server = null;

    /**
     * Sending Error
     *
     * @var bool
     */
    public $error = false;

    /**
     * Sending Response
     *
     * @var null|\Illuminate\Http\Client\Response
     */
    public $response = null;

    /**
     * Provide server instance by server name
     *
     * @return null|App\Http\Services\SmsServers\SmsSendingInterface
     */
    public static function getServerInstance(string $serverName, ?Server $server = null): ?SmsSendingInterface
    {
        self::$server = $server;

        $serverName = self::$namespace."\\$serverName";

        if (! class_exists($serverName)) {
            return null;
        }

        return new $serverName;
    }

    /**
     * Call get method
     *
     * @param  string  $method
     * @return mixed
     */
    public function __get($method)
    {
        if (! preg_match('/^(server)/', $method)) {
            return null;
        }

        return $this->returnServerColumns($method);
    }

    /**
     * Parse server column value
     *
     * @param  string  $column
     * @return string
     */
    private function returnServerColumns($column)
    {
        $column = Str::snake(preg_replace('/^(server)/', '', $column));

        return self::$server->{$column} ?? null;
    }
}
