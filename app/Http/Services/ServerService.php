<?php

namespace App\Http\Services;

use App\Exceptions\ErrorMessageException;
use App\Http\Repositories\ServerRepository;
use App\Models\Server;
use Illuminate\Http\Request;

class ServerService
{
    private $server;

    public function __construct(?ServerRepository $repo = null)
    {
        $this->server = $repo ?: new ServerRepository;
    }

    public function store(Request $request)
    {
        $serverData = array_merge(
            $request->only(['name', 'api_link', 'api_key', 'username', 'password', 'status', 'sending_limit', 'time_base', 'time_unit']),
            ['user_id' => auth()->id()]
        );

        try {
            $this->server->store($serverData);
        } catch (\Throwable $e) {
            throw new ErrorMessageException('Servers Create Error: '.$e->getMessage());
        }
    }

    public function update(Request $request, Server $server)
    {
        $serverData = $request->only('name', 'api_link', 'api_key', 'username', 'password', 'status', 'sending_limit', 'time_base', 'time_unit');
        try {
            $this->server->setContext($server)->update(array_filter($serverData));
        } catch (\Exception $e) {
            throw new ErrorMessageException('Server update Error: '.$e->getMessage());
        }
    }

    public function delete(Server $servers)
    {
        $this->server->setContext($servers)->delete();
    }
}
